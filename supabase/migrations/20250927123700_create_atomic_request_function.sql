-- This migration creates a PostgreSQL function to atomically create a request and its associated files.
-- This ensures that if file insertion fails, the request creation is rolled back.

CREATE OR REPLACE FUNCTION public.create_request_with_files(
    request_id TEXT,
    request_status TEXT,
    request_priority INTEGER,
    request_client_name TEXT,
    request_client_email TEXT,
    request_input JSONB,
    files_data JSONB
)
RETURNS SETOF public.requests AS $$
DECLARE
    new_request public.requests;
    file_record JSONB;
BEGIN
    -- Insert the request
    INSERT INTO public.requests (id, status, priority, client_name, client_email, input)
    VALUES (request_id, request_status, request_priority, request_client_name, request_client_email, request_input)
    RETURNING * INTO new_request;

    -- Insert associated files if any are provided
    IF files_data IS NOT NULL AND jsonb_array_length(files_data) > 0 THEN
        FOR file_record IN SELECT * FROM jsonb_array_elements(files_data)
        LOOP
            INSERT INTO public.files (request_id, name, type, size, url)
            VALUES (
                request_id,
                file_record->>'name',
                file_record->>'type',
                (file_record->>'size')::INTEGER,
                file_record->>'url'
            );
        END LOOP;
    END IF;

    -- Return the created request
    RETURN QUERY SELECT * FROM public.requests WHERE id = request_id;
END;
$$ LANGUAGE plpgsql;

-- Grant usage to the authenticated role
GRANT EXECUTE ON FUNCTION public.create_request_with_files(TEXT, TEXT, INTEGER, TEXT, TEXT, JSONB, JSONB) TO authenticated;