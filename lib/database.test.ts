/**
 * Unit tests for lib/database.ts
 * Framework: Vitest.
 * These tests mock @supabase/supabase-js createClient and validate:
 *  - happy paths
 *  - error propagation with informative messages
 *  - defaulting logic and timestamp handling
 *  - query composition (from/table names, filters, order, payloads)
 */
import { vi, test, expect, describe, beforeEach, afterEach } from "vitest";
import type { QuoteWithItems } from "./types";

// Mock the Supabase client
const mockQueryBuilder = {
  from: vi.fn().mockReturnThis(),
  select: vi.fn().mockReturnThis(),
  insert: vi.fn().mockReturnThis(),
  update: vi.fn().mockReturnThis(),
  delete: vi.fn().mockReturnThis(),
  eq: vi.fn().mockReturnThis(),
  in: vi.fn().mockReturnThis(),
  order: vi.fn().mockReturnThis(),
  limit: vi.fn().mockReturnThis(),
  single: vi.fn().mockResolvedValue({ data: {}, error: null }),
  maybeSingle: vi.fn().mockResolvedValue({ data: {}, error: null }),
  rpc: vi.fn().mockResolvedValue({ data: {}, error: null }),
  textSearch: vi.fn().mockReturnThis(),
};

const mockSupabaseClient = {
  from: vi.fn(() => mockQueryBuilder),
  rpc: vi.fn(() => mockQueryBuilder),
};

vi.mock('@supabase/ssr', () => ({
  createServerClient: vi.fn(() => mockSupabaseClient),
}));

vi.mock('@supabase/supabase-js', () => ({
    createClient: vi.fn(() => mockSupabaseClient),
}));


// Import module under test after mocking
import * as db from "./database";

beforeEach(() => {
  vi.clearAllMocks();
  vi.useFakeTimers();
  vi.setSystemTime(new Date("2025-01-02T03:04:05.000Z"));

  // Reset all mocks in the query builder
  Object.values(mockQueryBuilder).forEach(mockFn => {
    if (typeof mockFn.mockClear === 'function') {
      mockFn.mockClear();
      if(mockFn.mockReturnThis) mockFn.mockReturnThis();
    }
  });
  mockQueryBuilder.single.mockResolvedValue({ data: {}, error: null });
  mockQueryBuilder.maybeSingle.mockResolvedValue({ data: {}, error: null });
  mockQueryBuilder.rpc.mockResolvedValue({ data: {}, error: null });
  mockQueryBuilder.limit.mockResolvedValue({ data: [], error: null });
  mockSupabaseClient.from.mockClear().mockReturnValue(mockQueryBuilder);
  mockSupabaseClient.rpc.mockClear().mockReturnValue(mockQueryBuilder);

});

afterEach(() => {
  vi.useRealTimers();
});

describe("QUOTES", () => {
    test("createQuote inserts with defaults and returns created record", async () => {
      const input = { request_id: "req1", created_by: "user1", ai_generated_data: { a: 1 }, template_id: "tpl1" };
      const fakeQuote = { id: "q1", request_id: "req1", created_by: "user1", version: 1, status: "brouillon" };
      mockQueryBuilder.single.mockResolvedValueOnce({ data: fakeQuote, error: null });

      const result = await db.createQuote(input);

      expect(result).toBe(fakeQuote);
      expect(mockSupabaseClient.from).toHaveBeenCalledWith("quotes");
      expect(mockQueryBuilder.insert).toHaveBeenCalledTimes(1);
      const insertedArg = mockQueryBuilder.insert.mock.calls[0][0];
      expect(Array.isArray(insertedArg)).toBe(true);
      const row = insertedArg[0];
      expect(row).toMatchObject({
        request_id: "req1",
        created_by: "user1",
        modified_by: "user1",
        ai_generated_data: { a: 1 },
        template_id: "tpl1",
        version: 1,
        status: "brouillon"
      });
      expect(mockQueryBuilder.select).toHaveBeenCalled();
      expect(mockQueryBuilder.single).toHaveBeenCalled();
    });

    test("createQuote throws with supabase error message", async () => {
        mockQueryBuilder.single.mockResolvedValueOnce({ data: null, error: { message: "boom" } });
        await expect(db.createQuote({ request_id: "r", created_by: "u" }))
        .rejects.toThrow("Failed to create quote: boom");
    });

    test("getQuoteWithItems returns data", async () => {
        const fake: QuoteWithItems = { id: "q1", items: [], version: 1, status: 'brouillon', request_id: 'r1' };
        mockQueryBuilder.single.mockResolvedValueOnce({ data: fake, error: null });
        const res = await db.getQuoteWithItems("q1");
        expect(res).toEqual(fake);
        expect(mockSupabaseClient.from).toHaveBeenCalledWith('quotes_with_items');
        expect(mockQueryBuilder.select).toHaveBeenCalledWith("*");
        expect(mockQueryBuilder.eq).toHaveBeenCalledWith("id", "q1");
        expect(mockQueryBuilder.single).toHaveBeenCalled();
    });

    test("getQuoteWithItems returns null when error code PGRST116", async () => {
        mockQueryBuilder.single.mockResolvedValueOnce({ data: null, error: { code: "PGRST116", message: "No rows" } });
        await expect(db.getQuoteWithItems("nope")).resolves.toBeNull();
    });

    test("getQuoteWithItems throws other errors", async () => {
        mockQueryBuilder.single.mockResolvedValueOnce({ data: null, error: { code: "XX", message: "db fail" } });
        await expect(db.getQuoteWithItems("q1")).rejects.toThrow("Failed to get quote: db fail");
    });

    test("updateQuote merges updates, sets modified_by and updated_at", async () => {
        const nowISO = new Date().toISOString();
        const updated = { id: "q1", foo: "bar" };
        mockQueryBuilder.single.mockResolvedValueOnce({ data: updated, error: null });
        const res = await db.updateQuote("q1", { foo: "bar" } as any, "u2");
        expect(res).toBe(updated);
        expect(mockSupabaseClient.from).toHaveBeenCalledWith('quotes');
        expect(mockQueryBuilder.update).toHaveBeenCalledTimes(1);
        const arg = mockQueryBuilder.update.mock.calls[0][0];
        expect(arg.foo).toBe("bar");
        expect(arg.modified_by).toBe("u2");
        expect(arg.updated_at).toBe(nowISO);
        expect(mockQueryBuilder.eq).toHaveBeenCalledWith("id", "q1");
        expect(mockQueryBuilder.select).toHaveBeenCalled();
        expect(mockQueryBuilder.single).toHaveBeenCalled();
    });

    test("getQuotesByRequestId returns list ordered desc; empty fallback", async () => {
        mockQueryBuilder.order.mockResolvedValueOnce({ data: [], error: null });
        const res = await db.getQuotesByRequestId("reqX");
        expect(res).toEqual([]);
        expect(mockSupabaseClient.from).toHaveBeenCalledWith('quotes');
        expect(mockQueryBuilder.select).toHaveBeenCalledWith("*");
        expect(mockQueryBuilder.eq).toHaveBeenCalledWith("request_id", "reqX");
        expect(mockQueryBuilder.order).toHaveBeenCalledWith("version", { ascending: false });
    });

    test("getQuotesByRequestId throws on error", async () => {
        mockQueryBuilder.order.mockResolvedValueOnce({ data: null, error: { message: "oops" } });
        await expect(db.getQuotesByRequestId("reqX")).rejects.toThrow("Failed to get quotes: oops");
    });
});

describe("TEMPLATES", () => {
    test("getTemplatesByUser calls RPC without parameters", async () => {
      mockSupabaseClient.rpc.mockResolvedValueOnce({ data: [], error: null });
      const res = await db.getTemplatesByUser();
      expect(res).toEqual([]);
      expect(mockSupabaseClient.rpc).toHaveBeenCalledWith("get_user_templates");
    });
});