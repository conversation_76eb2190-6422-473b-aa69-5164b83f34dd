import type {
  Quote,
  LineItem,
  QuoteRecord,
  QuoteItemRecord,
  QuoteWithItems,
  Category,
  ClientInput
} from './types'

/**
 * Convertit un ancien Quote (format JSON) vers le nouveau format QuoteRecord + QuoteItemRecord[]
 */
export function convertLegacyQuoteToDatabase(
  legacyQuote: Quote,
  requestId: string,
  userId: string
): {
  quote: Omit<QuoteRecord, 'id' | 'created_at' | 'updated_at'>
  items: Omit<QuoteItemRecord, 'id' | 'quote_id' | 'created_at' | 'updated_at' | 'total_price'>[]
} {
  const quote = {
    request_id: requestId,
    version: 1,
    status: 'brouillon' as const,
    total_ht: legacyQuote.totals.subtotal,
    total_ttc: legacyQuote.totals.total,
    tva_rate: legacyQuote.totals.vatRate * 100, // Convert 0.2 to 20
    margin_rate: legacyQuote.totals.subtotal > 0 ? (legacyQuote.totals.margin / legacyQuote.totals.subtotal) * 100 : 0,
    created_by: userId,
    modified_by: userId,
    ai_generated_data: {
      original_quote: legacyQuote,
      items: legacyQuote.items.map(item => ({
        label: item.label,
        category: item.category,
        quantity: item.quantity,
        unit: item.unit,
        unitPrice: item.unitPrice
      })),
      summary: legacyQuote.notes || "Analyse par règles métier",
      aiEstimateTotal: legacyQuote.aiEstimateTotal,
      aiConfidence: legacyQuote.aiConfidence,
      usedProvider: "rule-based" as const,
      searchData: undefined
    },
    validity_days: 30
  }

  const items = legacyQuote.items.map((item, index) => ({
    category: item.category,
    description: item.label,
    quantity: item.quantity,
    unit: item.unit,
    unit_price: item.unitPrice,
    material_cost: 0, // Legacy format doesn't separate material/labor
    labor_cost: 0,
    complexity_factor: 1,
    order_index: index,
    is_optional: false,
    ai_suggested: true
  }))

  return { quote, items }
}

/**
 * Convertit une QuoteWithItems vers l'ancien format Quote pour compatibilité
 */
export function convertDatabaseQuoteToLegacy(quoteWithItems: QuoteWithItems): Quote {
  const items: LineItem[] = (quoteWithItems.items || []).map(item => ({
    id: item.id,
    label: item.description,
    category: item.category,
    quantity: item.quantity,
    unit: item.unit,
    unitPrice: item.unit_price,
    total: item.total_price,
    editable: true
  }))

  const vatRate = quoteWithItems.tva_rate / 100 // Convert 20 to 0.2
  let subtotal: number, margin: number, totalHT: number, vatAmount: number, total: number;

  // Prioritize quote totals if they are valid, otherwise calculate from items.
  // This handles both cases: where totals are authorative and where they need to be computed.
  if (quoteWithItems.total_ht > 0 && quoteWithItems.total_ttc > 0) {
    totalHT = quoteWithItems.total_ht
    total = quoteWithItems.total_ttc
    subtotal = totalHT / (1 + (quoteWithItems.margin_rate / 100))
    margin = totalHT - subtotal
    vatAmount = total - totalHT
  } else {
    subtotal = (quoteWithItems.items || []).reduce((s, it) => s + it.total_price, 0)
    margin = (quoteWithItems.margin_rate / 100) * subtotal
    totalHT = subtotal + margin
    vatAmount = vatRate * totalHT
    total = totalHT + vatAmount
  }

  return {
    id: quoteWithItems.id,
    currency: "EUR",
    items,
    totals: {
      subtotal: Math.round(subtotal * 100) / 100,
      discount: 0,
      margin: Math.round(margin * 100) / 100,
      vatRate,
      vatAmount: Math.round(vatAmount * 100) / 100,
      total: Math.round(total * 100) / 100
    },
    notes: quoteWithItems.internal_comments,
    aiEstimateTotal: quoteWithItems.ai_generated_data?.aiEstimateTotal ?? total,
    aiConfidence: quoteWithItems.ai_generated_data?.aiConfidence ?? 0.7
  }
}

/**
 * Calcule les totaux d'un devis à partir de ses items
 */
export function calculateQuoteTotals(items: QuoteItemRecord[], marginRate: number = 15, tvaRate: number = 20) {
  const subtotal = items.reduce((sum, item) => sum + item.total_price, 0)
  const margin = (marginRate / 100) * subtotal
  const totalHT = subtotal + margin
  const tvaAmount = (tvaRate / 100) * totalHT
  const totalTTC = totalHT + tvaAmount

  return {
    subtotal: Math.round(subtotal * 100) / 100,
    margin: Math.round(margin * 100) / 100,
    total_ht: Math.round(totalHT * 100) / 100,
    tva_amount: Math.round(tvaAmount * 100) / 100,
    total_ttc: Math.round(totalTTC * 100) / 100
  }
}

/**
 * Estime la durée d'un projet en heures basée sur les items
 */
export function estimateProjectDuration(items: QuoteItemRecord[]): number {
  const laborHours = items
    .filter(item => item.unit === 'h')
    .reduce((sum, item) => sum + item.quantity, 0)

  const surfaceItems = items
    .filter(item => item.unit === 'm2')
    .reduce((sum, item) => sum + item.quantity, 0)

  // Estimation: 2 heures par m² + heures de main d'œuvre directes
  const estimatedHours = laborHours + (surfaceItems * 2)

  return Math.max(4, Math.round(estimatedHours)) // Minimum 4 heures
}

/**
 * Génère des items de devis à partir d'un input client (pour IA)
 */
export function generateBaseQuoteItems(input: ClientInput): Omit<QuoteItemRecord, 'id' | 'quote_id' | 'created_at' | 'updated_at' | 'total_price'>[] {
  const items: Omit<QuoteItemRecord, 'id' | 'quote_id' | 'created_at' | 'updated_at' | 'total_price'>[] = []
  const surface = input.surfaceM2 || 20

  // Base sur le type de rénovation
  switch (input.typeRenovation) {
    case 'électricité':
      items.push(
        {
          category: 'électricité',
          description: 'Réfection installation électrique',
          quantity: surface,
          unit: 'm2',
          unit_price: 45,
          material_cost: 25,
          labor_cost: 20,
          complexity_factor: input.urgence === 'haute' ? 1.2 : 1,
          order_index: 0,
          is_optional: false,
          ai_suggested: true
        },
        {
          category: 'électricité',
          description: 'Main d\'œuvre électricité',
          quantity: Math.ceil(surface / 10),
          unit: 'h',
          unit_price: 65,
          material_cost: 0,
          labor_cost: 65,
          complexity_factor: 1,
          order_index: 1,
          is_optional: false,
          ai_suggested: true
        }
      )
      break

    case 'plomberie':
      items.push(
        {
          category: 'plomberie',
          description: 'Rénovation plomberie',
          quantity: surface,
          unit: 'm2',
          unit_price: 55,
          material_cost: 35,
          labor_cost: 20,
          complexity_factor: 1,
          order_index: 0,
          is_optional: false,
          ai_suggested: true
        },
        {
          category: 'plomberie',
          description: 'Main d\'œuvre plomberie',
          quantity: Math.ceil(surface / 8),
          unit: 'h',
          unit_price: 70,
          material_cost: 0,
          labor_cost: 70,
          complexity_factor: 1,
          order_index: 1,
          is_optional: false,
          ai_suggested: true
        }
      )
      break

    case 'peinture':
      items.push(
        {
          category: 'peinture',
          description: 'Peinture murs et plafonds',
          quantity: surface * 2.5, // Surface à peindre estimée
          unit: 'm2',
          unit_price: 15,
          material_cost: 8,
          labor_cost: 7,
          complexity_factor: 1,
          order_index: 0,
          is_optional: false,
          ai_suggested: true
        },
        {
          category: 'peinture',
          description: 'Préparation surfaces',
          quantity: Math.ceil(surface / 5),
          unit: 'h',
          unit_price: 45,
          material_cost: 5,
          labor_cost: 40,
          complexity_factor: 1,
          order_index: 1,
          is_optional: false,
          ai_suggested: true
        }
      )
      break

    case 'maçonnerie':
      items.push(
        {
          category: 'maçonnerie',
          description: 'Travaux de maçonnerie',
          quantity: surface,
          unit: 'm2',
          unit_price: 80,
          material_cost: 40,
          labor_cost: 40,
          complexity_factor: input.urgence === 'haute' ? 1.3 : 1.1,
          order_index: 0,
          is_optional: false,
          ai_suggested: true
        }
      )
      break

    default:
      items.push(
        {
          category: input.typeRenovation,
          description: `Rénovation ${input.typeRenovation}`,
          quantity: surface,
          unit: 'm2',
          unit_price: 50,
          material_cost: 30,
          labor_cost: 20,
          complexity_factor: 1,
          order_index: 0,
          is_optional: false,
          ai_suggested: true
        }
      )
  }

  // Ajouter des items de complexité si urgence haute
  if (input.urgence === 'haute') {
    items.push({
      category: 'autre',
      description: 'Surcoût urgence et disponibilité',
      quantity: 1,
      unit: 'u',
      unit_price: Math.round(items.reduce((sum, item) => sum + (item.quantity * item.unit_price), 0) * 0.15),
      material_cost: 0,
      labor_cost: 0,
      complexity_factor: 1,
      order_index: items.length,
      is_optional: true,
      ai_suggested: true
    })
  }

  return items
}

/**
 * Valide la cohérence d'un devis
 */
export function validateQuote(quote: QuoteWithItems): {
  isValid: boolean
  errors: string[]
  warnings: string[]
} {
  const errors: string[] = []
  const warnings: string[] = []

  // Vérifications obligatoires
  if (!quote.items || quote.items.length === 0) {
    errors.push('Le devis doit contenir au moins un item')
  }

  if (quote.total_ht <= 0) {
    errors.push('Le montant HT doit être positif')
  }

  if (quote.tva_rate > 0) {
    if (quote.total_ttc <= quote.total_ht) {
      errors.push('Le montant TTC doit être supérieur au montant HT')
    }
  } else {
    if (quote.total_ttc < quote.total_ht) {
      errors.push('Le montant TTC ne peut pas être inférieur au montant HT')
    }
  }

  // Vérifications des items
  quote.items?.forEach((item, index) => {
    if (!item.description || item.description.trim() === '') {
      errors.push(`L'item ${index + 1} doit avoir une description`)
    }

    if (item.quantity <= 0) {
      errors.push(`L'item ${index + 1} doit avoir une quantité positive`)
    }

    if (item.unit_price < 0) {
      errors.push(`L'item ${index + 1} ne peut pas avoir un prix unitaire négatif`)
    }

    if (item.unit_price === 0) {
      warnings.push(`L'item ${index + 1} a un prix unitaire de 0€`)
    }

    const expected = item.quantity * item.unit_price * item.complexity_factor
    if (Math.abs(item.total_price - expected) > 0.01) {
      warnings.push(`L'item ${index + 1} a un total incohérent avec le calcul automatique`)
    }
  })

  // Vérifications des totaux
  const calculatedTotals = calculateQuoteTotals(quote.items || [], quote.margin_rate, quote.tva_rate)

  if (Math.abs(calculatedTotals.total_ttc - quote.total_ttc) > 0.1) {
    warnings.push('Les totaux calculés ne correspondent pas aux totaux enregistrés')
  }

  // Vérifications métier
  if (quote.validity_days < 15) {
    warnings.push('La validité du devis est très courte (moins de 15 jours)')
  }

  if (quote.margin_rate < 5) {
    warnings.push('La marge est très faible (moins de 5%)')
  }

  if (quote.margin_rate > 40) {
    warnings.push('La marge est très élevée (plus de 40%)')
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * Compare deux versions d'un devis et retourne les différences
 */
export function compareQuoteVersions(oldQuote: QuoteWithItems, newQuote: QuoteWithItems): {
  quote: Record<string, { old: unknown, new: unknown }>
  items: {
    added: QuoteItemRecord[]
    removed: QuoteItemRecord[]
    modified: { old: QuoteItemRecord, new: QuoteItemRecord }[]
  }
} {
  const changes = {
    quote: {} as Record<string, { old: unknown, new: unknown }>,
    items: {
      added: [] as QuoteItemRecord[],
      removed: [] as QuoteItemRecord[],
      modified: [] as { old: QuoteItemRecord, new: QuoteItemRecord }[]
    }
  }

  // Compare quote-level fields
  const quoteFields: (keyof QuoteWithItems)[] = ['total_ht', 'total_ttc', 'tva_rate', 'margin_rate', 'status', 'validity_days', 'custom_conditions']

  quoteFields.forEach(field => {
    const oldValue = oldQuote[field]
    const newValue = newQuote[field]

    if (oldValue !== newValue) {
      changes.quote[field] = { old: oldValue, new: newValue }
    }
  })

  // Compare items
  const oldItems = oldQuote.items || []
  const newItems = newQuote.items || []

  const oldItemsMap = new Map(oldItems.map(item => [item.id, item]))
  const newItemsMap = new Map(newItems.map(item => [item.id, item]))

  // Find added items
  newItems.forEach(newItem => {
    if (!oldItemsMap.has(newItem.id)) {
      changes.items.added.push(newItem)
    }
  })

  // Find removed and modified items
  oldItems.forEach(oldItem => {
    const newItem = newItemsMap.get(oldItem.id)

    if (!newItem) {
      changes.items.removed.push(oldItem)
    } else {
      // Check if modified
      const itemFields: (keyof QuoteItemRecord)[] = ['description', 'quantity', 'unit_price', 'material_cost', 'labor_cost', 'complexity_factor']
      const isModified = itemFields.some(field => oldItem[field] !== newItem[field])

      if (isModified) {
        changes.items.modified.push({ old: oldItem, new: newItem })
      }
    }
  })

  return changes
}
