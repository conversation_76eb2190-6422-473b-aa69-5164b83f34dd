import { vi } from 'vitest';
import React from 'react';

// Make React available globally for tsx files
(global as any).React = React;

// Mock server-only
vi.mock('server-only', () => ({}));

// Mock next/headers
vi.mock('next/headers', () => ({
  cookies: () => ({
    get: vi.fn(),
    set: vi.fn(),
  }),
}));

// --- Comprehensive Supabase Mock ---

// This is the core query builder mock.
// It allows for chaining methods like .select().eq().maybeSingle()
const queryChainer = {
  select: vi.fn().mockReturnThis(),
  insert: vi.fn().mockReturnThis(),
  update: vi.fn().mockReturnThis(),
  delete: vi.fn().mockReturnThis(),
  eq: vi.fn().mockReturnThis(),
  in: vi.fn().mockReturnThis(),
  order: vi.fn().mockReturnThis(),
  limit: vi.fn().mockReturnThis(),
  single: vi.fn().mockResolvedValue({ data: {}, error: null }),
  maybeSingle: vi.fn().mockResolvedValue({ data: {}, error: null }),
  // Add any other methods you use in your code
};

const supabaseMock = {
  // from() is the entry point, it returns the chainer.
  from: vi.fn(() => queryChainer),
  // rpc() is a top-level method.
  rpc: vi.fn().mockResolvedValue({ data: {}, error: null }),
};

// Mock the auth client from @supabase/ssr
const supabaseAuthMock = {
    onAuthStateChange: vi.fn(() => ({ data: { subscription: { unsubscribe: vi.fn() } } })),
    getUser: vi.fn().mockResolvedValue({ data: { user: { id: 'test-user', email: '<EMAIL>' } } }),
    getSession: vi.fn().mockResolvedValue({ data: { session: { user: { id: 'test-user' } } } }),
    signInWithPassword: vi.fn().mockResolvedValue({ error: null }),
    signOut: vi.fn().mockResolvedValue({ error: null }),
    signUp: vi.fn().mockResolvedValue({ data: { user: { id: 'new-user' } }, error: null }),
    updateUser: vi.fn().mockResolvedValue({ data: { user: {} }, error: null }),
};

// Mock the client creation functions from @supabase/ssr
vi.mock('@supabase/ssr', () => ({
    createBrowserClient: vi.fn(() => ({
        auth: supabaseAuthMock,
        ...supabaseMock,
    })),
    createServerClient: vi.fn(() => ({
        auth: supabaseAuthMock,
        ...supabaseMock,
    })),
}));

// --- End Supabase Mock ---

// This is a setup for jsdom to extend Jest's `expect` matchers
// It's good practice to have this for React Testing Library.
try {
  require('@testing-library/jest-dom');
} catch (e) {
  // We don't want to fail if the dependency is not installed.
  // This is not critical for the tests to run.
}