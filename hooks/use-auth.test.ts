/**
 * Unit tests for hooks/use-auth.ts
 *
 * Testing stack:
 * - Framework: Vitest
 * - Library: @testing-library/react (renderHook, waitFor, act)
 *
 * These tests focus on the code paths shown in the diff:
 *  - useAuth: session bootstrap, profile fetch/creation, error handling, auth actions, helpers
 *  - useRequireAuth: redirection logic for unauthenticated/unauthorized cases
 *  - useProAuth: role-specific wrapper behavior
 *
 * External dependencies (Supabase, Next.js router) are mocked.
 */

import { renderHook, waitFor, act } from '@testing-library/react'
import { vi, beforeAll, beforeEach, describe, it, expect } from 'vitest'

const fn = vi.fn
const clearAllMocks = vi.clearAllMocks

vi.mock('@/lib/supabase-browser', () => ({ supabase: mockSupabaseClient }))
vi.mock('next/navigation', () => ({ useRouter: () => mockRouter }))

// Ensure Supabase env vars exist before module import (the hook creates a client at module scope)
process.env.NEXT_PUBLIC_SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL || 'http://supabase.local'
process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'anon-key'

// Router mock
const mockRouter = { replace: fn() }

// Subscriptions
const mockUnsubscribe = fn()
let authStateChangeCallback: ((event: string, session: any) => Promise<void> | void) | null = null

// Highly controllable Supabase client stub used across tests
const mockSupabaseClient: any = {
  auth: {
    getSession: fn(),                   // () => Promise<{ data: { session } }>
    onAuthStateChange: fn((cb: any) => {
      authStateChangeCallback = cb
      return { data: { subscription: { unsubscribe: mockUnsubscribe } } }
    }),
    signInWithPassword: fn(),           // ({ email, password }) => Promise<{ data, error }>
    signUp: fn(),                       // ({ email, password, options }) => Promise<{ data, error }>
    signOut: fn(),                      // () => Promise<{ error }>
    resetPasswordForEmail: fn(),        // (email, { redirectTo }) => Promise<{ error }>
    updateUser: fn(),                   // ({ password }) => Promise<{ error }>
  },
  from: fn()                            // (table) => query builder
}


// Helper to wire "profiles" table query chains per-test
type ProfilesHandlers = {
  select?: () => Promise<{ data: any, error: any | null }>
  insert?: (rows?: any[]) => Promise<{ data: any, error: any | null }>
  update?: (updates?: any) => Promise<{ data: any, error: any | null }>
}
function setProfilesHandlers(h: ProfilesHandlers) {
  mockSupabaseClient.from.mockImplementation((table: string) => {
    if (table !== 'profiles') {
      throw new Error(`Unexpected table: ${table}`)
    }
    return {
      // select('*').eq('id', ...).maybeSingle()
      select: fn((..._selArgs: any[]) => ({
        eq: fn((..._eqArgs: any[]) => ({
          maybeSingle: fn(() => (h.select ? h.select() : Promise.resolve({ data: null, error: { message: 'select not stubbed' } })))
        }))
      })),
      // insert([...]).select().single()
      insert: fn((..._insertArgs: any[]) => ({
        select: fn((..._s: any[]) => ({
          single: fn(() => (h.insert ? h.insert(_insertArgs[0]) : Promise.resolve({ data: null, error: { message: 'insert not stubbed' } })))
        }))
      })),
      // update(updates).eq('id', ...).select().single()
      update: fn((...updateArgs: any[]) => ({
        eq: fn((..._eqArgs: any[]) => ({
          select: fn((..._s: any[]) => ({
            single: fn(() => (h.update ? h.update(updateArgs[0]) : Promise.resolve({ data: null, error: { message: 'update not stubbed' } })))
          }))
        }))
      }))
    }
  })
}

let mod: any

beforeAll(async () => {
  // Import after mocks are set up so the module under test uses mocks
  mod = await import('./use-auth')
})

beforeEach(() => {
  clearAllMocks()
  mockRouter.replace = fn()
  authStateChangeCallback = null

  // Reasonable defaults
  mockSupabaseClient.auth.onAuthStateChange.mockImplementation((cb: any) => {
    authStateChangeCallback = cb
    return { data: { subscription: { unsubscribe: mockUnsubscribe } } }
  })
  mockSupabaseClient.auth.getSession.mockResolvedValue({ data: { session: null } })
  mockSupabaseClient.auth.signInWithPassword.mockResolvedValue({ data: { user: null }, error: null })
  mockSupabaseClient.auth.signUp.mockResolvedValue({ data: { user: null }, error: null })
  mockSupabaseClient.auth.signOut.mockResolvedValue({ error: null })
  mockSupabaseClient.auth.resetPasswordForEmail.mockResolvedValue({ error: null })
  mockSupabaseClient.auth.updateUser.mockResolvedValue({ error: null })
  mockSupabaseClient.from.mockReset()
})

describe('useAuth - bootstrap and profile retrieval', () => {
  it('fetches existing profile on mount (happy path)', async () => {
    const session = { user: { id: 'u1', email: '<EMAIL>', user_metadata: { name: 'Alice' } } }
    const profile = { id: 'u1', email: '<EMAIL>', name: 'Alice', role: 'client', created_at: '2025-01-01T00:00:00Z' }

    mockSupabaseClient.auth.getSession.mockResolvedValue({ data: { session } })
    setProfilesHandlers({
      select: async () => ({ data: profile, error: null })
    })

    const { result } = renderHook(() => mod.useAuth())

    await waitFor(() => expect(result.current.loading).toBe(false))
    expect(result.current.user).toEqual(profile)
    expect(result.current.isAuthenticated).toBe(true)
    expect(result.current.isClient).toBe(true)
    expect(result.current.isPro).toBe(false)
    expect(result.current.isAdmin).toBe(false)
  })

  it('creates profile when not found (PGRST116) and sets user', async () => {
    const session = { user: { id: 'u2', email: '<EMAIL>', user_metadata: {} } }
    const newProfile = { id: 'u2', email: '<EMAIL>', name: 'new', role: 'client', created_at: '2025-01-02T00:00:00Z' }

    mockSupabaseClient.auth.getSession.mockResolvedValue({ data: { session } })
    setProfilesHandlers({
      select: async () => ({ data: null, error: null }),
      insert: async () => ({ data: newProfile, error: null })
    })
    const { result } = renderHook(() => mod.useAuth())

    await waitFor(() => expect(result.current.loading).toBe(false))
    expect(result.current.user).toEqual(newProfile)
    expect(mockSupabaseClient.from).toHaveBeenCalledWith('profiles')
  })

  it('logs error and leaves user null on unexpected profile fetch error', async () => {
    const session = { user: { id: 'u3', email: '<EMAIL>' } }
    mockSupabaseClient.auth.getSession.mockResolvedValue({ data: { session } })
    setProfilesHandlers({
      select: async () => ({ data: null, error: { code: '500', message: 'DB blew up' } })
    })
    const spyErr = vi.spyOn(console, 'error').mockImplementation(() => {})

    const { result } = renderHook(() => mod.useAuth())

    await waitFor(() => expect(result.current.loading).toBe(false))
    expect(result.current.user).toBeNull()
    expect(spyErr).toHaveBeenCalled()
    spyErr.mockRestore()
  })

  it('cleans up auth subscription on unmount', async () => {
    const { unmount } = renderHook(() => mod.useAuth())
    unmount()
    expect(mockUnsubscribe).toHaveBeenCalledTimes(1)
  })

  it('handles SIGNED_IN and SIGNED_OUT events', async () => {
    // Prepare: initial no session
    setProfilesHandlers({
      select: async () => ({ data: { id: 'u4', email: '<EMAIL>', name: 'X', role: 'pro', created_at: '2025-01-03' }, error: null })
    })
    const { result } = renderHook(() => mod.useAuth())
    await waitFor(() => expect(result.current.loading).toBe(false))
    expect(result.current.user).toBeNull()

    // SIGNED_IN -> should fetch user
    const session = { user: { id: 'u4', email: '<EMAIL>' } }
    // The hook's implementation of onAuthStateChange calls getUser, which in turn calls getSession.
    // We must mock getSession to return the new session for the test to pass.
    mockSupabaseClient.auth.getSession.mockResolvedValue({ data: { session } })
    await act(async () => {
      authStateChangeCallback && (await authStateChangeCallback('SIGNED_IN', session))
    })
    await waitFor(() => expect(result.current.user?.id).toBe('u4'))
    expect(result.current.isPro).toBe(true)

    // SIGNED_OUT -> clears user. The hook's listener handles this directly.
    await act(async () => {
      authStateChangeCallback && (await authStateChangeCallback('SIGNED_OUT', null))
    })
    // No waitFor needed if we check the final state, as `act` flushes effects.
    expect(result.current.user).toBeNull()
    expect(result.current.loading).toBe(false)
  })
})

describe('useAuth - auth actions', () => {
  it('signIn success returns success=true and calls Supabase with credentials', async () => {
    const data = { user: { id: 'uid' } }
    mockSupabaseClient.auth.signInWithPassword.mockResolvedValue({ data, error: null })

    const { result } = renderHook(() => mod.useAuth())
    const res = await result.current.signIn('<EMAIL>', 'secret')
    expect(res).toEqual({ success: true, data })
    expect(mockSupabaseClient.auth.signInWithPassword).toHaveBeenCalledWith({ email: '<EMAIL>', password: 'secret' })
  })

  it('signIn failure returns success=false with message', async () => {
    mockSupabaseClient.auth.signInWithPassword.mockResolvedValue({ data: null, error: new Error('Invalid creds') })
    const { result } = renderHook(() => mod.useAuth())
    const res = await result.current.signIn('<EMAIL>', 'wrong')
    expect(res.success).toBe(false)
    expect(String(res.error)).toContain('Invalid')
  })

  it('signUp success uses provided name/role and defaults when omitted', async () => {
    const data = { user: { id: 'uid2' } }
    mockSupabaseClient.auth.signUp.mockResolvedValue({ data, error: null })

    const { result } = renderHook(() => mod.useAuth())
    // explicit name
    const r1 = await result.current.signUp('<EMAIL>', 'pw', 'Pro User')
    expect(r1).toEqual({ success: true, data })
    // default name from email and role 'client'
    const r2 = await result.current.signUp('<EMAIL>', 'pw')
    expect(r2).toEqual({ success: true, data })

    // Validate payloads
    const calls = mockSupabaseClient.auth.signUp.mock.calls
    expect(calls[0][0]).toMatchObject({ email: '<EMAIL>', password: 'pw', options: { data: { name: 'Pro User' } } })
    expect(calls[1][0]).toMatchObject({ email: '<EMAIL>', password: 'pw', options: { data: { name: 'foo' } } })
  })

  it('signOut success resets user and returns success=true', async () => {
    const session = { user: { id: 'u5', email: '<EMAIL>' } }
    const profile = { id: 'u5', email: '<EMAIL>', role: 'client', created_at: '2025-01-04' }
    mockSupabaseClient.auth.getSession.mockResolvedValue({ data: { session } })
    setProfilesHandlers({ select: async () => ({ data: profile, error: null }) })

    const { result } = renderHook(() => mod.useAuth())
    await waitFor(() => expect(result.current.user?.id).toBe('u5'))

    await act(async () => {
      const out = await result.current.signOut()
      expect(out).toEqual({ success: true })
    })
    await waitFor(() => expect(result.current.user).toBeNull())
  })

  it('updateProfile returns error when no user is logged in', async () => {
    const { result } = renderHook(() => mod.useAuth())
    const resp = await result.current.updateProfile({ name: 'Nobody' })
    expect(resp).toEqual({ success: false, error: 'No user logged in' })
  })

  it('updateProfile persists and merges updates', async () => {
    const session = { user: { id: 'u6', email: '<EMAIL>' } }
    const profile = { id: 'u6', email: '<EMAIL>', name: 'Old', role: 'client', created_at: '2025-01-05' }
    mockSupabaseClient.auth.getSession.mockResolvedValue({ data: { session } })
    setProfilesHandlers({
      select: async () => ({ data: profile, error: null }),
      update: async (updates?: any) => ({ data: { ...profile, ...updates }, error: null })
    })

    const { result } = renderHook(() => mod.useAuth())
    await waitFor(() => expect(result.current.user?.id).toBe('u6'))

    await act(async () => {
      const resp = await result.current.updateProfile({ name: 'New' })
      expect(resp.success).toBe(true)
    })

    await waitFor(() => expect(result.current.user?.name).toBe('New'))
    expect(result.current.isAdmin).toBe(false)
    expect(result.current.isPro).toBe(false)
  })

  it('resetPassword uses redirect to /auth/reset-password', async () => {
    const { result } = renderHook(() => mod.useAuth())
    const resp = await result.current.resetPassword('<EMAIL>')
    expect(resp).toEqual({ success: true })
    expect(mockSupabaseClient.auth.resetPasswordForEmail).toHaveBeenCalled()
    const args = mockSupabaseClient.auth.resetPasswordForEmail.mock.calls[0]
    expect(args[0]).toBe('<EMAIL>')
    expect(args[1].redirectTo).toContain('/auth/reset-password')
  })

  it('updatePassword success', async () => {
    const { result } = renderHook(() => mod.useAuth())
    const resp = await result.current.updatePassword('new-strong-password')
    expect(resp).toEqual({ success: true })
    expect(mockSupabaseClient.auth.updateUser).toHaveBeenCalledWith({ password: 'new-strong-password' })
  })
})

describe('useRequireAuth - routing behavior', () => {
  it('redirects to /auth/login when not authenticated', async () => {
    // No session by default
    const { result } = renderHook(() => mod.useRequireAuth())
    await waitFor(() => expect(result.current.loading).toBe(false))
    expect(mockRouter.replace).toHaveBeenCalledWith('/auth/login')
  })

  it('redirects to /unauthorized when role mismatch', async () => {
    const session = { user: { id: 'u7', email: '<EMAIL>' } }
    const profile = { id: 'u7', email: '<EMAIL>', role: 'client', name: 'C', created_at: '2025-01-06' }
    mockSupabaseClient.auth.getSession.mockResolvedValue({ data: { session } })
    setProfilesHandlers({ select: async () => ({ data: profile, error: null }) })

    const { result } = renderHook(() => mod.useRequireAuth('pro'))
    await waitFor(() => expect(result.current.loading).toBe(false))
    expect(mockRouter.replace).toHaveBeenCalledWith('/unauthorized')
  })

  it('allows admin even if required role is pro', async () => {
    const session = { user: { id: 'u8', email: '<EMAIL>' } }
    const profile = { id: 'u8', email: '<EMAIL>', role: 'admin', name: 'A', created_at: '2025-01-07' }
    mockSupabaseClient.auth.getSession.mockResolvedValue({ data: { session } })
    setProfilesHandlers({ select: async () => ({ data: profile, error: null }) })

    const { result } = renderHook(() => mod.useRequireAuth('pro'))
    await waitFor(() => expect(result.current.loading).toBe(false))
    expect(mockRouter.replace).not.toHaveBeenCalled()
  })
})

describe('useProAuth - shorthand wrapper', () => {
  it('permits pro user', async () => {
    const session = { user: { id: 'u9', email: '<EMAIL>' } }
    const profile = { id: 'u9', email: '<EMAIL>', role: 'pro', name: 'P', created_at: '2025-01-08' }
    mockSupabaseClient.auth.getSession.mockResolvedValue({ data: { session } })
    setProfilesHandlers({ select: async () => ({ data: profile, error: null }) })

    const { result } = renderHook(() => mod.useProAuth())
    await waitFor(() => expect(result.current.loading).toBe(false))
    expect(mockRouter.replace).not.toHaveBeenCalled()
  })

  it('redirects non-pro to /unauthorized', async () => {
    const session = { user: { id: 'u10', email: '<EMAIL>' } }
    const profile = { id: 'u10', email: '<EMAIL>', role: 'client', name: 'C2', created_at: '2025-01-09' }
    mockSupabaseClient.auth.getSession.mockResolvedValue({ data: { session } })
    setProfilesHandlers({ select: async () => ({ data: profile, error: null }) })

    const { result } = renderHook(() => mod.useProAuth())
    await waitFor(() => expect(result.current.loading).toBe(false))
    expect(mockRouter.replace).toHaveBeenCalledWith('/unauthorized')
  })
})
