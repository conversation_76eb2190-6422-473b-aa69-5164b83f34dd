/**
 * Unit tests for app/actions/pro-actions.ts
 *
 * Testing framework: Vitest
 * These tests mock "@/lib/database" and verify:
 *  - Happy paths return data
 *  - Error handling behavior (throw vs. log + [] fallback)
 *  - Null/undefined data returns []
 *  - Correct Supabase query chain: from -> select("*") -> order("created_at", { ascending: false }) -> limit(N)
 */

import { describe, it, expect, vi, beforeEach } from "vitest"
import { createServerClientForRequest } from "@/lib/database"

// Mock the query builder chain
const mockQueryBuilder = {
  select: vi.fn().mockReturnThis(),
  order: vi.fn().mockReturnThis(),
  limit: vi.fn().mockResolvedValue({ data: [], error: null }),
  single: vi.fn().mockResolvedValue({ data: {}, error: null }),
};

// Mock the supabase client
const mockSupabaseClient = {
  from: vi.fn(() => mockQueryBuilder),
};

// Mock the database module
vi.mock("@/lib/database", () => ({
  createServerClientForRequest: vi.fn(() => Promise.resolve(mockSupabaseClient)),
}));

// Import the functions under test AFTER the mock is declared
import { getRecentRequests, getRecentQuotes, getDashboardStatsAction } from "./pro-actions"


beforeEach(() => {
  vi.clearAllMocks();
  // Reset mock implementation for each test
  mockQueryBuilder.select.mockReturnThis();
  mockQueryBuilder.order.mockReturnThis();
  mockQueryBuilder.single.mockResolvedValue({ data: {}, error: null });
});

describe("getRecentRequests", () => {
  it("returns recent requests and calls the correct Supabase chain", async () => {
    const sample = [{ id: "r1" }, { id: "r2" }];
    mockQueryBuilder.limit.mockResolvedValueOnce({ data: sample, error: null });

    const res = await getRecentRequests();

    expect(res).toEqual(sample);
    expect(createServerClientForRequest).toHaveBeenCalledTimes(1);
    expect(mockSupabaseClient.from).toHaveBeenCalledWith("requests");
    expect(mockQueryBuilder.select).toHaveBeenCalledWith("*, files (*)");
    expect(mockQueryBuilder.order).toHaveBeenCalledWith("created_at", { ascending: false });
    expect(mockQueryBuilder.limit).toHaveBeenCalledWith(50);
  });

  it("throws an error when Supabase returns an error", async () => {
    mockQueryBuilder.limit.mockResolvedValueOnce({ data: null, error: { message: "db down" } });

    await expect(getRecentRequests()).rejects.toThrowError(
      "Failed to load requests: db down"
    );
  });

  it("returns an empty array when Supabase returns null/undefined data", async () => {
    mockQueryBuilder.limit.mockResolvedValueOnce({ data: null, error: null });

    const res = await getRecentRequests();
    expect(res).toEqual([]);
  });
});

describe("getRecentQuotes", () => {
  it("returns recent quotes and calls the correct Supabase chain", async () => {
    const sample = [{ id: "q1" }, { id: "q2" }];
    mockQueryBuilder.limit.mockResolvedValueOnce({ data: sample, error: null });

    const res = await getRecentQuotes();

    expect(res).toEqual(sample);
    expect(createServerClientForRequest).toHaveBeenCalledTimes(1);
    expect(mockSupabaseClient.from).toHaveBeenCalledWith("quotes_with_items");
    expect(mockQueryBuilder.select).toHaveBeenCalledWith("*");
    expect(mockQueryBuilder.order).toHaveBeenCalledWith("created_at", { ascending: false });
    expect(mockQueryBuilder.limit).toHaveBeenCalledWith(20);
  });

  it("throws an error when Supabase returns an error", async () => {
    mockQueryBuilder.limit.mockResolvedValueOnce({ data: null, error: { message: "boom" } });
    await expect(getRecentQuotes()).rejects.toThrowError(
      "Failed to load quotes: boom"
    );
  });

  it("returns [] when Supabase returns null/undefined data", async () => {
    mockQueryBuilder.limit.mockResolvedValueOnce({ data: null, error: null });
    const res = await getRecentQuotes();
    expect(res).toEqual([]);
  });
});

describe("getDashboardStatsAction", () => {
    it("returns stats from the database", async () => {
        const stats = { pending_requests: 1 };
        mockQueryBuilder.single.mockResolvedValueOnce({ data: stats, error: null });

        const res = await getDashboardStatsAction();

        expect(res).toEqual(stats);
        expect(createServerClientForRequest).toHaveBeenCalledTimes(1);
        expect(mockSupabaseClient.from).toHaveBeenCalledWith("dashboard_stats");
        expect(mockQueryBuilder.select).toHaveBeenCalledWith("*");
        expect(mockQueryBuilder.single).toHaveBeenCalled();
    });
});