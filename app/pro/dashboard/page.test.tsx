import React from 'react'
import { render, screen, waitFor } from '@testing-library/react'
import { describe, it, expect, beforeEach, vi } from 'vitest'
import '@testing-library/jest-dom'

import ProDashboardPage from './page'
import { useProAuth } from '@/hooks/use-auth'
import { getDashboardStatsAction, getRecentRequests, getRecentQuotes } from '@/app/actions/pro-actions'

// Mock dependencies
vi.mock('@/hooks/use-auth');
vi.mock('@/app/actions/pro-actions');
vi.mock('next/navigation', () => ({
    useRouter: () => ({
        push: vi.fn(),
    }),
}));

const mockUser = { id: 'pro_123', name: '<PERSON>' };

const makeRequest = (overrides: Partial<any> = {}) => ({
  id: `req_${Math.random().toString(36).slice(2, 8)}`,
  client_name: 'Client Exemple',
  ai_summary: '<PERSON>ésumé AI',
  status: 'en_attente',
  priority: 2,
  created_at: new Date().toISOString(),
  ...overrides,
});

const makeQuote = (overrides: Partial<any> = {}) => ({
  id: `quo_${Math.random().toString(36).slice(2, 8)}`,
  client_name: 'Client Quote',
  total_ttc: 1234.56,
  status: 'validé',
  version: 1,
  created_at: new Date().toISOString(),
  ...overrides,
});

describe('ProDashboardPage', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    (useProAuth as vi.Mock).mockReturnValue({ user: mockUser, loading: false });
    (getDashboardStatsAction as vi.Mock).mockResolvedValue({
      pending_requests: 1,
      sent_quotes: 2,
      accepted_quotes: 3,
      total_accepted_amount: 10000,
    });
    (getRecentRequests as vi.Mock).mockResolvedValue([makeRequest()]);
    (getRecentQuotes as vi.Mock).mockResolvedValue([makeQuote()]);
  });

  it('renders stats cards when data is loaded', async () => {
    render(<ProDashboardPage />);
    expect(await screen.findByText('Demandes en attente')).toBeInTheDocument();
    expect(screen.getByText('1')).toBeInTheDocument();
    expect(screen.getByText('Devis envoyés')).toBeInTheDocument();
    expect(screen.getByText('2')).toBeInTheDocument();
  });

  it('renders recent requests table', async () => {
    (getRecentRequests as vi.Mock).mockResolvedValue([makeRequest({ client_name: 'Alice' })]);
    render(<ProDashboardPage />);
    expect(await screen.findByText('Alice')).toBeInTheDocument();
  });

  it('renders recent quotes table', async () => {
    (getRecentQuotes as vi.Mock).mockResolvedValue([makeQuote({ client_name: 'Bob Quote' })]);
    render(<ProDashboardPage />);
    expect(await screen.findByText('Bob Quote')).toBeInTheDocument();
  });
});