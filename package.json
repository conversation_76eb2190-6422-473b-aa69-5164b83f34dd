{"name": "my-v0-project", "version": "0.1.0", "private": true, "type": "module", "scripts": {"build": "next build", "dev": "next dev", "lint": "eslint . --format=compact", "start": "next start", "test": "vitest"}, "dependencies": {"@ai-sdk/google": "latest", "@edge-runtime/vm": "latest", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "latest", "@radix-ui/react-alert-dialog": "latest", "@radix-ui/react-aspect-ratio": "latest", "@radix-ui/react-avatar": "latest", "@radix-ui/react-checkbox": "latest", "@radix-ui/react-collapsible": "latest", "@radix-ui/react-context-menu": "latest", "@radix-ui/react-dialog": "latest", "@radix-ui/react-dropdown-menu": "latest", "@radix-ui/react-hover-card": "latest", "@radix-ui/react-label": "latest", "@radix-ui/react-menubar": "latest", "@radix-ui/react-navigation-menu": "latest", "@radix-ui/react-popover": "latest", "@radix-ui/react-progress": "latest", "@radix-ui/react-radio-group": "latest", "@radix-ui/react-scroll-area": "latest", "@radix-ui/react-select": "latest", "@radix-ui/react-separator": "latest", "@radix-ui/react-slider": "latest", "@radix-ui/react-slot": "latest", "@radix-ui/react-switch": "latest", "@radix-ui/react-tabs": "latest", "@radix-ui/react-toast": "latest", "@radix-ui/react-toggle": "latest", "@radix-ui/react-toggle-group": "latest", "@radix-ui/react-tooltip": "latest", "@supabase/ssr": "latest", "@supabase/supabase-js": "latest", "@tailwindcss/postcss": "^4.1.14", "@types/debug": "latest", "@vitest/browser": "latest", "@vitest/ui": "latest", "ai": "latest", "app": "latest", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "latest", "date-fns": "4.1.0", "embla-carousel-react": "latest", "geist": "1.4.2", "happy-dom": "latest", "input-otp": "latest", "lucide-react": "^0.454.0", "next": "15.2.4", "next-themes": "latest", "path": "latest", "react": "^19.2.0", "react-day-picker": "latest", "react-dom": "^19.2.0", "react-hook-form": "latest", "react-resizable-panels": "latest", "recharts": "latest", "sonner": "latest", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "url": "latest", "vaul": "latest", "zod": "^3.25.76"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@testing-library/dom": "^10.4.1", "@testing-library/jest-dom": "^6.9.1", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^22.18.8", "@types/react": "^19.2.2", "@types/react-dom": "^19.2.1", "eslint": "^9.37.0", "eslint-config-next": "15.5.3", "eslint-formatter-compact": "^8.40.0", "jsdom": "^27.0.0", "postcss": "^8.5.6", "server-only": "^0.0.1", "tailwindcss": "^4.1.14", "typescript": "^5.9.3", "vitest": "^3.2.4"}}